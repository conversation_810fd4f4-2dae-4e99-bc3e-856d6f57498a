package com.iflytek.lynxiao.eval.service.mission.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.iflytek.lynxiao.common.exception.LynxiaoException;
import com.iflytek.lynxiao.common.feign.portal.FlowVersionApi;
import com.iflytek.lynxiao.data.domain.VersionStatus;
import com.iflytek.lynxiao.data.dto.FlowVersionDTO;
import com.iflytek.lynxiao.eval.autogen.generated.domain.GeneratedEvalMarkRecord;
import com.iflytek.lynxiao.eval.autogen.generated.domain.GeneratedEvalMission;
import com.iflytek.lynxiao.eval.autogen.generated.domain.GeneratedEvalMissionAssign;
import com.iflytek.lynxiao.eval.autogen.generated.domain.GeneratedEvalQueryGroup;
import com.iflytek.lynxiao.eval.domain.AscribeMode;
import com.iflytek.lynxiao.eval.domain.AscribeStatus;
import com.iflytek.lynxiao.eval.domain.StrategyConfig;
import com.iflytek.lynxiao.eval.dto.mission.EvalMissionAscribeProcessDTO;
import com.iflytek.lynxiao.eval.dto.mission.EvalMissionCreateDTO;
import com.iflytek.lynxiao.eval.dto.mission.EvalMissionDTO;
import com.iflytek.lynxiao.eval.dto.mission.StandardConfig;
import com.iflytek.lynxiao.eval.dto.standard.MarkDimensionGroupDTO;
import com.iflytek.lynxiao.eval.dto.standard.MarkDimensionModuleDTO;
import com.iflytek.lynxiao.eval.dto.standard.MarkStandardDTO;
import com.iflytek.lynxiao.eval.repository.EvalMarkRecordRepository;
import com.iflytek.lynxiao.eval.repository.EvalMissionAssignRepository;
import com.iflytek.lynxiao.eval.repository.EvalMissionRepository;
import com.iflytek.lynxiao.eval.repository.EvalQueryGroupRepository;
import com.iflytek.lynxiao.eval.service.mission.EvalMissionAssignService;
import com.iflytek.lynxiao.eval.service.mission.EvalMissionService;
import com.iflytek.lynxiao.eval.service.standard.MarkDimensionGroupService;
import com.iflytek.lynxiao.eval.service.standard.MarkDimensionModuleService;
import com.iflytek.lynxiao.eval.service.standard.MarkStandardService;
import com.iflytek.lynxiao.eval.utils.AuditingEntityUtil;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;

import static com.iflytek.lynxiao.eval.utils.CatalogTreeUtils.getAllChildrenCode;

/**
 * 多query 测评任务管理
 *
 * <AUTHOR>
 */
@Log4j2
@Service
@Transactional(rollbackFor = Exception.class)
public class EvalMissionServiceImpl implements EvalMissionService {

    public static final String MISSION_MODULE_CODE = "CPRW";
    private final EvalMissionRepository evalMissionRepository;
    private final FlowVersionApi flowVersionApi;
    private final EvalMarkRecordRepository markRecordRepository;
    private final EvalMissionAssignRepository evalMissionAssignRepository;
    private final EvalQueryGroupRepository evalQueryGroupRepository;
    private final EvalMissionAssignService evalMissionAssignService;
    private final MarkStandardService markStandardService;
    private final MarkDimensionGroupService markDimensionGroupService;
    private final MarkDimensionModuleService markDimensionModuleService;
    private final EvalMissionAscribePipelineHandler evalMissionAscribePipelineHandler;

    public EvalMissionServiceImpl(EvalMissionRepository evalMissionRepository, FlowVersionApi flowVersionApi, EvalMarkRecordRepository markRecordRepository, EvalMissionAssignRepository evalMissionAssignRepository,
                                  EvalQueryGroupRepository evalQueryGroupRepository,
                                  EvalMissionAssignService evalMissionAssignService, MarkStandardService markStandardService, MarkDimensionGroupService markDimensionGroupService, MarkDimensionModuleService markDimensionModuleService, EvalMissionAscribePipelineHandler evalMissionAscribePipelineHandler) {
        this.evalMissionRepository = evalMissionRepository;
        this.flowVersionApi = flowVersionApi;
        this.markRecordRepository = markRecordRepository;
        this.evalMissionAssignRepository = evalMissionAssignRepository;
        this.evalQueryGroupRepository = evalQueryGroupRepository;
        this.evalMissionAssignService = evalMissionAssignService;
        this.markStandardService = markStandardService;
        this.markDimensionGroupService = markDimensionGroupService;
        this.markDimensionModuleService = markDimensionModuleService;
        this.evalMissionAscribePipelineHandler = evalMissionAscribePipelineHandler;
    }


    @Override
    public String create(EvalMissionCreateDTO dto) {
        log.debug("创建测评任务，参数：{}", dto);
        // 1. 校验参数
        checkCreateParam(dto);
        // 2. 初始化评测任务基本信息
        GeneratedEvalMission generatedEvalMission = innitBaseInfo(dto);
        // 3. 构建测评标准配置
        updateStandard(dto.getStandardId(), generatedEvalMission);

        AuditingEntityUtil.fillCreateValue(generatedEvalMission);
        this.evalMissionRepository.save(generatedEvalMission);
        this.evalMissionAssignService.createUserAssign(generatedEvalMission.getId(), Long.valueOf(dto.getQueryGroupId()), dto.getDataAssign());
        return generatedEvalMission.getId().toString();
    }

    @Override
    public void update(String id, EvalMissionCreateDTO dto) {
        GeneratedEvalMission mission = this.findGeneratedEvalMission(id);
        Long origionStandardId = mission.getStandardId();
        // 已经启用的不允许编辑
        if (mission.getEnabled()) {
            throw new LynxiaoException("测评任务已启用，不能编辑!");
        }
        checkCreateParam(dto);
        mission.setName(dto.getName());
        mission.setDescription(dto.getDescription());
        processStrategyConfig(dto.getStrategyConfig());
        mission.setStrategyConfig(JSONArray.toJSONString(dto.getStrategyConfig()));
        // 更新任务分配表
        updateDataAssign(mission, dto);
        // 非发布状态才可以编辑完整信息
        checkUpdateItem(mission, dto);
        if (mission.getStatus() != VersionStatus.PUBLISHED) {
            BeanUtil.copyProperties(dto, mission, "id", "queryGroupId", "dataAssign", "extendFields");
            mission.setQueryGroupId(Long.valueOf(dto.getQueryGroupId()));
            mission.setExtendFields(dto.getExtendFields() != null ? String.join(",", dto.getExtendFields()) : null);

            if (!Objects.equals(origionStandardId, Long.valueOf(dto.getStandardId()))) {
                // 重新构建测评标准配置
                updateStandard(dto.getStandardId(), mission);
            }
        }
        mission.setGroupNumber(dto.getDataAssign().size());
        AuditingEntityUtil.fillUpdateValue(mission);
        this.evalMissionRepository.save(mission);
    }

    /**
     * 更新任务分配信息
     * 当任务状态为草稿时，直接删除历史分配记录后重新分配；非草稿状态时更细任务分配表
     *
     * @param mission 任务
     * @param dto     创建任务参数
     */
    private void updateDataAssign(GeneratedEvalMission mission, EvalMissionCreateDTO dto) {

        if (mission.getStatus() != VersionStatus.DRAFT) {
            // 非草稿状态 更新分配表
            this.evalMissionAssignService.updateUserAssign(mission, Long.valueOf(dto.getQueryGroupId()), dto.getDataAssign());
            return;
        }

        // 草稿状态下，直接更新任务分配表 1.删除历史分配记录  2.重新分配
        //1.删除历史分配记录
        List<GeneratedEvalMissionAssign> allByMissionIdAndDeletedFalse = this.evalMissionAssignRepository.findAllByMissionIdAndDeletedFalse(mission.getId());
        if (CollectionUtil.isNotEmpty(allByMissionIdAndDeletedFalse)) {
            allByMissionIdAndDeletedFalse.forEach(generatedEvalMissionAssign -> generatedEvalMissionAssign.setDeleted(true));
            this.evalMissionAssignRepository.saveAll(allByMissionIdAndDeletedFalse);
        }

        //2.重新分配
        this.evalMissionAssignService.createUserAssign(mission.getId(), Long.valueOf(dto.getQueryGroupId()), dto.getDataAssign());
    }

    @Override
    public Page<EvalMissionDTO> page(String search, String catalogCode, Pageable pageable) {
        log.debug("分页查询测评任务，参数：{}", search);

        //获取当前code所有子目录的code
        MarkDimensionModuleDTO markDimensionModuleDTO = this.markDimensionModuleService.find(MISSION_MODULE_CODE);
        List<String> allChildrenCode = getAllChildrenCode(catalogCode, markDimensionModuleDTO.getCatalogTree());

        Page<GeneratedEvalMission> generatedEvalMissions = StringUtils.isNotBlank(search) ?
                this.evalMissionRepository.search(search, allChildrenCode, pageable) : this.evalMissionRepository.findAllByDeletedFalse(allChildrenCode, pageable);

        // 获取任务分配信息,组装列表属性
        List<Long> missionIds = generatedEvalMissions.getContent().stream().map(GeneratedEvalMission::getId).collect(Collectors.toList());
        List<GeneratedEvalMissionAssign> missionAssigns = this.evalMissionAssignRepository.findAllByMissionIdInAndDeletedFalse(missionIds);
        // 获取query集信息
        List<Long> queryGroupIds = generatedEvalMissions.getContent().stream().map(GeneratedEvalMission::getQueryGroupId).toList();
        List<GeneratedEvalQueryGroup> queryGroups = this.evalQueryGroupRepository.findAllById(queryGroupIds);
        Map<Long, GeneratedEvalQueryGroup> queryGroupMap = queryGroups.stream().collect(Collectors.toMap(GeneratedEvalQueryGroup::getId, item -> item));

        // 根据任务id分组得到每个任务的人员分配
        Map<Long, List<GeneratedEvalMissionAssign>> missionAssignMap = missionAssigns.stream().collect(Collectors.groupingBy(GeneratedEvalMissionAssign::getMissionId));
        return generatedEvalMissions.map(evalMission -> {
            EvalMissionDTO evalMissionDTO = EvalMissionDTO.of(evalMission);
            List<GeneratedEvalMissionAssign> missionAssignList = missionAssignMap.get(evalMission.getId());

            // query分配与完成情况
            int assignQueryCount = missionAssignList.stream().map(GeneratedEvalMissionAssign::getAssignedCount).mapToInt(Integer::intValue).sum();
            evalMissionDTO.setAssignQueryCount(assignQueryCount);

            int completeCount = missionAssignList.stream().map(GeneratedEvalMissionAssign::getCompletedCount).mapToInt(Integer::intValue).sum();
            evalMissionDTO.setCompleteQueryCount(completeCount);

            //该任务所有分配的任务都已完成
            if (!CollectionUtils.isEmpty(missionAssignList)) {
                evalMissionDTO.setMarkFinished(assignQueryCount == completeCount);
            }

            if (CollectionUtil.isNotEmpty(missionAssignList)) {
                // 每组下的人员列表

                Map<String, List<String>> groupUserAssignments = missionAssignList.stream().collect(Collectors.groupingBy(GeneratedEvalMissionAssign::getUserGroup, Collectors.mapping(GeneratedEvalMissionAssign::getAccount, Collectors.toList())));
                evalMissionDTO.setGroupUserAssignments(groupUserAssignments);
                evalMissionDTO.setUserCount(groupUserAssignments.values().stream().mapToInt(List::size).sum());
                // query的条数
                evalMissionDTO.setQueryCount(queryGroupMap.get(evalMission.getQueryGroupId()).getSize());
            }
            return evalMissionDTO;
        });
    }

    @Override
    public EvalMissionDTO findById(String id) {
        log.debug("根据ID查询测评任务，参数：{}", id);
        GeneratedEvalMission evalMission = this.evalMissionRepository.findById(Long.valueOf(id)).orElseThrow(() -> new LynxiaoException("未获取到测评任务"));
        EvalMissionDTO evalMissionDTO = EvalMissionDTO.of(evalMission);
        fillStrategyProcessName(evalMissionDTO);
        return evalMissionDTO;
    }

    @Override
    public void delete(String id) {
        log.debug("删除测评任务，参数：{}", id);
        GeneratedEvalMission generatedEvalMission = this.findGeneratedEvalMission(id);
        // 启用的不允许删除
        if (generatedEvalMission.getEnabled()) {
            throw new LynxiaoException("测评任务已启用，不能删除");
        }
        // 删除任务分配详情
        List<GeneratedEvalMissionAssign> allByMissionIdAndDeletedFalse = this.evalMissionAssignRepository.findAllByMissionIdAndDeletedFalse(generatedEvalMission.getId());
        if (CollectionUtil.isNotEmpty(allByMissionIdAndDeletedFalse)) {
            allByMissionIdAndDeletedFalse.forEach(generatedEvalMissionAssign -> generatedEvalMissionAssign.setDeleted(true));
            this.evalMissionAssignRepository.saveAll(allByMissionIdAndDeletedFalse);
        }
        generatedEvalMission.setDeleted(true);
        // 删除任务
        this.evalMissionRepository.save(generatedEvalMission);
    }

    @Override
    public void publish(String id) {
        log.debug("发布测评任务，参数：{}", id);
        GeneratedEvalMission generatedEvalMission = this.findGeneratedEvalMission(id);
        if (generatedEvalMission.getStatus() == VersionStatus.PUBLISHED) {
            throw new LynxiaoException("测评任务已发布");
        }
        generatedEvalMission.setStatus(VersionStatus.PUBLISHED);
        AuditingEntityUtil.fillUpdateValue(generatedEvalMission);
        this.evalMissionRepository.save(generatedEvalMission);
    }

    @Override
    public void copy(String id) {
        log.info("复制测评任务，参数：{}", id);
        GeneratedEvalMission generatedEvalMission = this.findGeneratedEvalMission(id);

        EvalMissionCreateDTO copyMissionDto = BeanUtil.copyProperties(generatedEvalMission, EvalMissionCreateDTO.class, "strategyConfig", "extendFields");
        copyMissionDto.setName(generatedEvalMission.getName() + "-copy");
        List<StrategyConfig> strategyConfigs = JSONArray.parseArray(generatedEvalMission.getStrategyConfig(), StrategyConfig.class);
        //清空策略配置的id
        strategyConfigs.forEach(item -> item.setId(StringUtils.EMPTY));
        copyMissionDto.setStrategyConfig(strategyConfigs);
        copyMissionDto.setExtendFields(Arrays.stream(generatedEvalMission.getExtendFields().split(","))
                .map(String::trim)
                .collect(Collectors.toList()));

        //获取任务分配情况
        List<GeneratedEvalMissionAssign> missionAssigns = this.evalMissionAssignRepository.findAllByMissionIdAndDeletedFalse(generatedEvalMission.getId());
        Map<String, List<String>> groupUserAssignments = missionAssigns.stream().collect(Collectors.groupingBy(GeneratedEvalMissionAssign::getUserGroup, Collectors.mapping(GeneratedEvalMissionAssign::getAccount, Collectors.toList())));
        copyMissionDto.setDataAssign(groupUserAssignments);

        this.create(copyMissionDto);
    }

    @Override
    public void archived(String id) {
        log.debug("归档测评任务，参数：{}", id);
        GeneratedEvalMission generatedEvalMission = this.findGeneratedEvalMission(id);
        if (generatedEvalMission.getStatus() == VersionStatus.ARCHIVED) {
            throw new LynxiaoException("测评任务已归档");
        }
        generatedEvalMission.setStatus(VersionStatus.ARCHIVED);
        AuditingEntityUtil.fillUpdateValue(generatedEvalMission);
        this.evalMissionRepository.save(generatedEvalMission);
    }

    @Override
    public void enable(String id, Boolean enable) {
        log.debug("发布测评任务，参数：{}", id);
        GeneratedEvalMission generatedEvalMission = this.findGeneratedEvalMission(id);
        // 未发布的不允许启用
        if (enable) {
            if (generatedEvalMission.getStatus() != VersionStatus.PUBLISHED) {
                throw new LynxiaoException("测评任务未发布，不能启用");
            }
        }
        generatedEvalMission.setEnabled(enable);
        AuditingEntityUtil.fillUpdateValue(generatedEvalMission);
        this.evalMissionRepository.save(generatedEvalMission);
    }

    @Override
    public void ascribe(String missionId) {
        log.debug("测评任务自动归因，missionId：{}", missionId);

        //检查当前归因状态，正在归因时不可重复开启归因
        GeneratedEvalMission generatedEvalMission = this.findGeneratedEvalMission(missionId);
        if (generatedEvalMission.getAscribeStatus() == AscribeStatus.PROCESSING) {
            throw new LynxiaoException("测评任务正在归因中，请稍后再试");
        }

        //判断是否全部标注完成
        List<GeneratedEvalMissionAssign> missionAssigns = this.evalMissionAssignRepository.findAllByMissionIdAndDeletedFalse(Long.valueOf(missionId));
        if (missionAssigns.stream().anyMatch(item -> StringUtils.isNotBlank(item.getTodoIdxs()))) {
            throw new LynxiaoException("测评任务未全完成，不能归因");
        }

        try {
            this.evalMissionAscribePipelineHandler.onData(missionId);
        } catch (InterruptedException e) {
            log.error("测评任务归因失败，missionId:{}", missionId, e);
            throw new LynxiaoException("测评任务归因失败，请稍后再试");
        }
    }


    @Override
    public EvalMissionAscribeProcessDTO ascribeProcess(String missionId) {
        log.debug("获取测评任务归因进度，missionId：{}", missionId);
        GeneratedEvalMission generatedEvalMission = this.findGeneratedEvalMission(missionId);

        //找到所有的测评记录
        List<GeneratedEvalMarkRecord> markRecords = this.markRecordRepository.findByMissionIdAndDeletedFalse(Long.valueOf(missionId));
        EvalMissionAscribeProcessDTO response = new EvalMissionAscribeProcessDTO();
        response.setTotal(markRecords.size());
        response.setSuccess((int) markRecords.stream().filter(item -> item.getAscribeSuccess() != null && item.getAscribeSuccess()).count());
        response.setFail((int) markRecords.stream().filter(item -> item.getAscribeSuccess() != null && !item.getAscribeSuccess()).count());
        response.setStatus(generatedEvalMission.getAscribeStatus());
        response.setStartTime(generatedEvalMission.getAscribeStartTs());
        response.setEndTime(generatedEvalMission.getAscribeEndTs());

        return response;
    }

    @Override
    public List<FlowVersionDTO> findSceneList(String missionId) {
        log.debug("查询测评任务关联的场景策略列表，missionId：{}", missionId);

        GeneratedEvalMission generatedEvalMission = this.findGeneratedEvalMission(missionId);
        List<StrategyConfig> strategyConfigs = JSONArray.parseArray(generatedEvalMission.getStrategyConfig(), StrategyConfig.class);
        List<String> processIds = strategyConfigs.stream().map(StrategyConfig::getProcessId).toList();

        //2.根据列表返回流程版本列表
        List<FlowVersionDTO> flowVersionList = flowVersionApi.findByProcessIdIn(processIds);
        return flowVersionList.stream().map(flowVersion -> BeanUtil.copyProperties(flowVersion, FlowVersionDTO.class)).collect(Collectors.toList());
    }

    /**
     * 更新测评任务的测评标准配置
     *
     * @param standardId 测评标准ID
     * @param mission    测评任务对象
     */
    private void updateStandard(String standardId, GeneratedEvalMission mission) {
        MarkStandardDTO standardDto = this.markStandardService.findById(standardId);
        StandardConfigBuildParams standardConfigBuildParams = buildStandardConfigBuildParams(standardDto);
        StandardConfig standardConfig = StandardConfig.of(standardConfigBuildParams);
        mission.setStandardConfig(JSONObject.toJSONString(standardConfig));
        mission.setStandardId(Long.valueOf(standardId));
    }

    private void fillStrategyProcessName(EvalMissionDTO evalMissionDTO) {
        List<String> processIds = evalMissionDTO.getStrategyConfig().stream().map(StrategyConfig::getProcessId).toList();
        Map<String, FlowVersionDTO> flowVersionMap = flowVersionApi.findByProcessIdIn(processIds).stream().collect(Collectors.toMap(FlowVersionDTO::getProcessId, item -> item));

        for (StrategyConfig strategyConfig : evalMissionDTO.getStrategyConfig()) {
            if (strategyConfig.isOffline()) {
                continue;
            }
            FlowVersionDTO flowVersionDTO = flowVersionMap.get(strategyConfig.getProcessId());
            strategyConfig.setProcessName(flowVersionDTO.getName());
        }
    }

    private GeneratedEvalMission innitBaseInfo(EvalMissionCreateDTO dto) {
        GeneratedEvalMission generatedEvalMission = BeanUtil.copyProperties(dto, GeneratedEvalMission.class, "id");

        processStrategyConfig(dto.getStrategyConfig());

        // 组的个数
        generatedEvalMission.setExtendFields(dto.getExtendFields() != null ? String.join(",", dto.getExtendFields()) : null);
        generatedEvalMission.setStrategyConfig(JSONArray.toJSONString(dto.getStrategyConfig()));
        generatedEvalMission.setGroupNumber(dto.getDataAssign().size());
        generatedEvalMission.setEnabled(Boolean.FALSE);
        generatedEvalMission.setStatus(VersionStatus.DRAFT);
        generatedEvalMission.setAscribeStatus(AscribeStatus.PENDING);
        generatedEvalMission.setDeleted(false);
        return generatedEvalMission;
    }

    private void processStrategyConfig(List<StrategyConfig> strategyConfigs) {
        if (CollectionUtils.isEmpty(strategyConfigs)) {
            throw new LynxiaoException("测评任务策略配置不能为空");
        }

        for (StrategyConfig strategyConfig : strategyConfigs) {
            if (StringUtils.isNotBlank(strategyConfig.getId())) {
                continue;
            }

            strategyConfig.setId(UUID.randomUUID().toString());
            //badcase分析时  归因时的流程与processId一致
            if (strategyConfig.getAscribeMode() == AscribeMode.BAD)
                strategyConfig.setId4Ascribe(strategyConfig.getProcessId());
        }
    }

    /**
     * 构建二级目录的数据库对象的参数
     */
    private StandardConfigBuildParams buildStandardConfigBuildParams(MarkStandardDTO standardDTO) {
        StandardConfigBuildParams result = new StandardConfigBuildParams();
        result.setStandardConfig(standardDTO);
        if (StringUtils.isNotEmpty(standardDTO.getRecallDimsGroupId())) {
            result.setRecall(this.markDimensionGroupService.findById(standardDTO.getRecallDimsGroupId()));
        }

        if (StringUtils.isNotEmpty(standardDTO.getChatDimsGroupId())) {
            result.setChat(this.markDimensionGroupService.findById(standardDTO.getChatDimsGroupId()));
        }

        if (StringUtils.isNotEmpty(standardDTO.getIgnoreQueryDimsGroupId())) {
            result.setQueryIgnore(this.markDimensionGroupService.findById(standardDTO.getIgnoreQueryDimsGroupId()));
        }

        if (StringUtils.isNotEmpty(standardDTO.getIgnoreDocDimsGroupId())) {
            result.setDocIgnore(markDimensionGroupService.findById(standardDTO.getIgnoreDocDimsGroupId()));
        }
        return result;
    }

    /**
     * 创建测评任务参数检查
     * 1.判断当前测评任务各组间是否存在重复的标注人
     * 2.获取离线query集id
     */
    private void checkCreateParam(EvalMissionCreateDTO dto) {
        // 收集所有用户及其所属的组
        Map<String, List<String>> userGroups = new HashMap<>();
        for (Map.Entry<String, List<String>> entry : dto.getDataAssign().entrySet()) {
            String group = entry.getKey();
            for (String user : entry.getValue()) {
                userGroups.computeIfAbsent(user, k -> new ArrayList<>()).add(group);
            }
        }

        // 检查是否有用户属于多个组
        List<String> duplicateUsers = userGroups.entrySet().stream()
                .filter(entry -> entry.getValue().size() > 1)
                .map(Map.Entry::getKey)
                .toList();

        if (!duplicateUsers.isEmpty()) {
            throw new LynxiaoException("存在某一个用户被分到多个组中,请检查分组配置!");
        }

    }

    private GeneratedEvalMission findGeneratedEvalMission(String id) {
        return this.evalMissionRepository.findById(Long.valueOf(id)).orElseThrow(() -> new LynxiaoException("未获取到测评任务"));
    }

    private void checkUpdateItem(GeneratedEvalMission mission, EvalMissionCreateDTO dto) {
        if (mission.getStatus() == VersionStatus.PUBLISHED) {
            // 发布状态不可修改数据集
            if (!Objects.equals(String.valueOf(mission.getQueryGroupId()), dto.getQueryGroupId())) {
                throw new LynxiaoException("任务已发布,不可修改关联的query数据集!");
            }
        }
    }

    @Setter
    @Getter
    public static class StandardConfigBuildParams {
        private MarkDimensionGroupDTO recall;
        private MarkDimensionGroupDTO chat;
        private MarkDimensionGroupDTO queryIgnore;
        private MarkDimensionGroupDTO docIgnore;
        private MarkStandardDTO standardConfig;
    }
}
