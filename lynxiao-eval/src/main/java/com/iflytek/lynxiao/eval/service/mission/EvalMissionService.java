package com.iflytek.lynxiao.eval.service.mission;

import com.iflytek.lynxiao.data.dto.FlowVersionDTO;
import com.iflytek.lynxiao.eval.dto.mission.EvalMissionAscribeProcessDTO;
import com.iflytek.lynxiao.eval.dto.mission.EvalMissionCreateDTO;
import com.iflytek.lynxiao.eval.dto.mission.EvalMissionDTO;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import java.util.List;

/**
 * 测评任务管理
 *
 * <AUTHOR>
 */
public interface EvalMissionService {

    /**
     * 创建
     */
    String create(EvalMissionCreateDTO dto);

    /**
     * 编辑任务
     */
    void update(String id, EvalMissionCreateDTO dto);

    /**
     * 分页查询任务列表
     *
     * @param search   搜索条件
     * @param pageable 分页参数
     * @return 任务列表
     */
    Page<EvalMissionDTO> page(String search, String catalogCode, Pageable pageable);

    /**
     * 根据ID查询任务
     *
     * @param id 任务ID
     * @return EvalMissionDTO
     */
    EvalMissionDTO findById(String id);

    /**
     * 删除任务
     */
    void delete(String id);

    /**
     * 发布任务
     */
    void publish(String id);

    /**
     * 复制任务
     */
    void copy(String id);

    /**
     * 归档任务
     */
    void archived(String id);

    /**
     * 启用任务
     */
    void enable(String id, Boolean enable);

    /**
     * 自动归因 ： 对标注完成的测评任务进行归因处理
     *
     * @param missionId 测评任务id
     */
    void ascribe(String missionId);

    /**
     * 获取归因分析进度
     * @param missionId 任务id
     */
    EvalMissionAscribeProcessDTO ascribeProcess(String missionId);

    List<FlowVersionDTO> findSceneList(String missionId);
}
