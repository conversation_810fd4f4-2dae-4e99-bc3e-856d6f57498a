package com.iflytek.lynxiao.eval.controller.mission;

import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.iflytek.lynxiao.eval.dto.mission.EvalMissionCreateDTO;
import com.iflytek.lynxiao.eval.service.mission.EvalMissionAssignService;
import com.iflytek.lynxiao.eval.service.mission.EvalMissionService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import lombok.extern.log4j.Log4j2;
import org.springframework.data.domain.Pageable;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import skynet.boot.annotation.EnableSkynetSwagger2;
import skynet.boot.pandora.api.ApiResponse;

/**
 * 测评任务表管理
 *
 * <AUTHOR>
 */
@Tag(name = "测评任务表管理")
@RestController
@RequestMapping("/eval/api/v1/mission")
@EnableSkynetSwagger2
@Log4j2
public class EvalMissionController {

    @Resource
    protected EvalMissionService evalMissionService;

    @Resource
    private EvalMissionAssignService missionAssignService;

    @Operation(summary = "创建")
    @PostMapping("/create")
    public ApiResponse create(@RequestBody @Validated EvalMissionCreateDTO dto) {
        return new ApiResponse(JSONObject.of("data", evalMissionService.create(dto)));
    }

    @Operation(summary = "编辑")
    @PostMapping("/update/{id}")
    public ApiResponse update(@PathVariable String id, @RequestBody @Validated EvalMissionCreateDTO dto) {
        evalMissionService.update(id, dto);
        return new ApiResponse();
    }

    @Operation(summary = "根据任务id查询任务信息")
    @GetMapping("/findById/{id}")
    public ApiResponse findById(@PathVariable String id) {
        return new ApiResponse(JSONObject.of("data", evalMissionService.findById(id)));
    }

    @Operation(summary = "分页查询任务列表")
    @GetMapping("/page")
    public ApiResponse page(@RequestParam(value = "search", required = false) String search,
                            @RequestParam(value = "catalogCode") String catalogCode,
                            Pageable pageable) {
        return new ApiResponse(JSONObject.from(evalMissionService.page(search, catalogCode, pageable)));
    }

    @Operation(summary = "删除query集")
    @DeleteMapping("/delete/{id}")
    public ApiResponse delete(@PathVariable String id) {
        evalMissionService.delete(id);
        return new ApiResponse();
    }

    @Operation(summary = "发布任务")
    @GetMapping("/publish/{id}")
    public ApiResponse publish(@PathVariable String id) {
        evalMissionService.publish(id);
        return new ApiResponse();
    }

    @Operation(summary = "copy任务")
    @GetMapping("/copy/{id}")
    public ApiResponse copy(@PathVariable String id) {
        evalMissionService.copy(id);
        return new ApiResponse();
    }

    @Operation(summary = "自动归因")
    @GetMapping("/ascribe/{id}")
    public ApiResponse ascribe(@PathVariable String id) {
        evalMissionService.ascribe(id);
        return new ApiResponse();
    }

    @Operation(summary = "自动归因进度")
    @GetMapping("/ascribe-process/{id}")
    public ApiResponse ascribeProcess(@PathVariable String id) {
        return new ApiResponse(JSONObject.of("data", evalMissionService.ascribeProcess(id)));
    }

    @Operation(summary = "归档任务")
    @GetMapping("/archived/{id}")
    public ApiResponse archived(@PathVariable String id) {
        evalMissionService.archived(id);
        return new ApiResponse();
    }

    @Operation(summary = "启、禁用任务")
    @GetMapping("/enable/{id}")
    public ApiResponse enable(@PathVariable String id, @RequestParam(name = "enable") Boolean enable) {
        evalMissionService.enable(id, enable);
        return new ApiResponse();
    }

    @Operation(summary = "场景策略过滤范围")
    @GetMapping(value = "scene-list/{missionId}")
    public ApiResponse findSceneList(@PathVariable String missionId) {
        return new ApiResponse(JSONObject.of("data", evalMissionService.findSceneList(missionId)));
    }

    @Operation(summary = "我的任务")
    @GetMapping("/my-list")
    public ApiResponse myList() {
        return new ApiResponse(JSONObject.of("data", JSONArray.from(missionAssignService.myList())));
    }

    @Operation(summary = "标注角色人员获取")
    @GetMapping("/mark-user")
    public ApiResponse queryMarkUser() {
        return new ApiResponse(JSONObject.of("data", JSONArray.from(missionAssignService.queryMarkUser())));
    }

    @Operation(summary = "我的任务详情")
    @GetMapping("/my-detail")
    public ApiResponse myDetail(@RequestParam("id") String id) {
        return new ApiResponse(JSONObject.of("data", missionAssignService.myDetail(id)));
    }

    @Operation(summary = "切换当前标注query")
    @GetMapping("/my-detail/switch-query")
    public ApiResponse switchQuery(@RequestParam("missionId") String missionId, @RequestParam(value = "recordId", required = false) String recordId,
                                   @RequestParam("type") String type) {
        // todo 返回的参数，实际就是任务详情
        return new ApiResponse(JSONObject.of("data", JSONObject.from(missionAssignService.querySwitch(missionId, recordId, type))));
    }

}
