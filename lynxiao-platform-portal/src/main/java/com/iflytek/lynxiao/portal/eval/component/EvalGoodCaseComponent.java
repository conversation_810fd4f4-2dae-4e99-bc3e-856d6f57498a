package com.iflytek.lynxiao.portal.eval.component;

import cn.hutool.core.lang.UUID;
import com.alibaba.fastjson2.JSONObject;
import com.iflytek.lynxiao.common.exception.LynxiaoException;
import com.iflytek.lynxiao.common.feign.content.ContentApi;
import com.iflytek.lynxiao.portal.eval.component.core.*;
import com.iflytek.lynxiao.portal.eval.component.core.impl.ProcTraceFetcherFactory;
import com.iflytek.lynxiao.portal.eval.component.domain.*;
import com.iflytek.turing.astrolink.service.WorkflowProcessService;
import com.iflytek.turing.astrolink.service.dto.WorkflowProcess;
import io.jsonwebtoken.lang.Assert;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import skynet.boot.pandora.FuncServiceHandler;
import skynet.boot.pandora.annotation.SkynetPandoraFuncHandler;
import skynet.boot.pandora.api.ApiRequest;
import skynet.boot.pandora.api.ApiResponse;
import skynet.boot.pandora.exception.PandoraException;

import java.util.List;

/**
 * 竞品分析组件
 * <p>
 * 输入：流程id、url，执行完整的goodCase分析过程（包括goodUrl没有召回的预评估过程）
 * </p>
 *
 * <AUTHOR>  2025/3/6 09:03
 */
@Slf4j
@SkynetPandoraFuncHandler
public class EvalGoodCaseComponent implements FuncServiceHandler {

    private final WorkflowProcessService workflowProcessService;
    private final ExistService existService;
    private final ContentApi urlNormalizer;
    private final PreEvalNodeInvoker preEvalNodeInvoker;
    private final ProcTraceFetcherFactory procTraceFetcherFactory;

    public EvalGoodCaseComponent(WorkflowProcessService workflowProcessService, ExistService existService,
                                 ContentApi urlNormalizer, PreEvalNodeInvoker preEvalNodeInvoker, ProcTraceFetcherFactory procTraceFetcherFactory) {
        this.workflowProcessService = workflowProcessService;
        this.existService = existService;
        this.urlNormalizer = urlNormalizer;
        this.preEvalNodeInvoker = preEvalNodeInvoker;
        this.procTraceFetcherFactory = procTraceFetcherFactory;
    }

    @Override
    public ApiResponse process(ApiRequest apiRequest) throws PandoraException {
        log.debug("good-case eval start, apiRequest={}", apiRequest);

        AscribeInputGoodCase ascribeInputGoodCase = apiRequest.getPayload().to(AscribeInputGoodCase.class);
        checkInput(ascribeInputGoodCase);
        ascribeInputGoodCase.set_traceId("eval_ascribe_good" + UUID.fastUUID().toString().substring(0, 8));
        log.info("goodcase traceId:{}", ascribeInputGoodCase.get_traceId());

        ProcTraceFetcher procTraceFetcher = procTraceFetcherFactory.buildFetcher(ascribeInputGoodCase.getProcessId(), ascribeInputGoodCase.getTraceId());
        ProcTraceFetcherParam fetcherParam = ProcTraceFetcherParam.of(ascribeInputGoodCase, apiRequest);
        String processId = procTraceFetcher.fetchWorkflowProcessId(fetcherParam);

        WorkflowProcess workflowProcess = workflowProcessService.getWorkflowProcess(processId, false);
        if (workflowProcess == null) {
            throw new PandoraException("流程不存在");
        }
        fetcherParam.setProcessId(processId);
        fetcherParam.setWorkflowProcess(workflowProcess);

        // 1. 分析id或者url是否存在
        GoodCaseContext goodCaseContext = new GoodCaseContext(ascribeInputGoodCase);
        ExistCheckResult existCheckResult = existService.process(ascribeInputGoodCase.getGoodId(), ascribeInputGoodCase.getGoodUrl(), workflowProcess, goodCaseContext);
        goodCaseContext.setExistCheckResult(existCheckResult);

        // 2. 拿到query执行轨迹
        ProcTraceFetcherResult fetcherResult = procTraceFetcher.process(fetcherParam);
        List<TraceLogItem> traceLogItems = fetcherResult.getTraceLogItems();
        goodCaseContext.setTraceLogs(traceLogItems);

        // 3. 判断是否需要mock，以及能否mock
        if (ascribeInputGoodCase.getNeedMock() == 0) {
            // 不需要mock，直接返回
            log.info("good-case eval not need mock, goodCaseId:{}, goodCaseUrl:{}, doc:{}", ascribeInputGoodCase.getGoodId(), ascribeInputGoodCase.getGoodUrl(), goodCaseContext.getDoc());
            return new ApiResponse(JSONObject.from(goodCaseContext.computeOutput()));
        }
        if (goodCaseContext.getDoc() == null) {
            // id或者url对应的文档doc不存在
            log.warn("good-case eval doc not exist, goodCaseId:{}, goodCaseUrl:{}", ascribeInputGoodCase.getGoodId(), ascribeInputGoodCase.getGoodUrl());
            goodCaseContext.getExistCheckResult().setReason4CrawledFailed();
            return new ApiResponse(JSONObject.from(goodCaseContext.computeOutput()));
        }

        // 4.mock分析
        executeMockAnalysis(goodCaseContext, workflowProcess);

        // 5. 输出结果
        AscribeOutputGoodCase ascribeOutputGoodCase = goodCaseContext.computeOutput();
        return new ApiResponse(JSONObject.from(ascribeOutputGoodCase));
    }


    /**
     * mock文档，进行分析
     * 除召回节点外 检查哪些节点需要mock  需要mock的条件是 good doc不存在于该节点的初始返回结果中
     *
     * @param goodCaseContext 分析会话的上下文
     * @param workflowProcess mock需要使用的流程
     */
    private void executeMockAnalysis(GoodCaseContext goodCaseContext, WorkflowProcess workflowProcess) {
        PreEvalNodeChain preEvalNodeChain = new PreEvalNodeChain(goodCaseContext.getTraceLogs());

        preEvalNodeChain.checkNeedMock(goodCaseContext.getDocId(), goodCaseContext.getTraceLogs());

        // 直接执行组件链逻辑
        List<PreEvalNode> allCompChain = preEvalNodeChain.getNodeChain();
        for (PreEvalNode preEvalNode : allCompChain) {
            if (preEvalNode.isCanBeMock()) {
                // 执行组件
                List<JSONObject> outputDocs = preEvalNodeInvoker.process(workflowProcess, goodCaseContext, preEvalNode);
                // 记录执行结果
                goodCaseContext.addCompResult(preEvalNode.getNodeId(), outputDocs);
            }
        }
    }

    private void checkInput(AscribeInputGoodCase ascribeInputGoodCase) {

        Assert.notNull(ascribeInputGoodCase, "请求参数不能为空");
        // processId和traceId不能同时为空
        if (StringUtils.isAllBlank(ascribeInputGoodCase.getProcessId(), ascribeInputGoodCase.getTraceId())) {
            throw new LynxiaoException("processId和traceId不能同时为空");
        }

        Assert.hasText(ascribeInputGoodCase.getProcessId(), "processId can not be empty");
        Assert.hasText(ascribeInputGoodCase.getQuery(), "query can not be empty");
        Assert.isTrue(StringUtils.isNotBlank(ascribeInputGoodCase.getGoodId())
                || StringUtils.isNotBlank(ascribeInputGoodCase.getGoodUrl()), "goodId and goodUrl can not be empty at the same time");

        if (StringUtils.isNotBlank(ascribeInputGoodCase.getGoodUrl())) {
            // url规范化
            String normalizedUrl = this.urlNormalizer.normalizeUrl(ascribeInputGoodCase.getGoodUrl());
            if (StringUtils.isBlank(normalizedUrl)) {
                log.error("normalizeUrl failed, url:{}", ascribeInputGoodCase.getGoodUrl());
                throw new LynxiaoException("规范后的url为空，请检查输入的url:" + ascribeInputGoodCase.getGoodUrl());
            }
            ascribeInputGoodCase.setGoodUrl(normalizedUrl);
        }
    }
}
