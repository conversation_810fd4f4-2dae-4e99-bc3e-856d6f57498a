package com.iflytek.lynxiao.portal.utils;

import com.iflytek.lynxiao.common.exception.LynxiaoException;
import com.iflytek.skybox.contract.domain.BoxUserBase;
import com.iflytek.skybox.contract.service.BoxUserContextHolder;

public class CurrentUserUtils {
    public static String getCurrentUser() {
        BoxUserBase currentUser = BoxUserContextHolder.getCurrentUser();
        if (currentUser != null) {
            return currentUser.getName();
        } else {
            return "System";
        }
    }

    public static String getCurrentAccount() {
        BoxUserBase currentUser = BoxUserContextHolder.getCurrentUser();
        if (currentUser != null) {
            return currentUser.getAccount();
        } else {
            throw new LynxiaoException("获取用户信息失败。");
        }
    }
}
