package com.iflytek.lynxiao.portal.eval.component.core;

import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.iflytek.lynxiao.common.datashard.DatashardDecoder;
import com.iflytek.lynxiao.portal.document.dto.site.SiteVersionGroupDTO;
import com.iflytek.lynxiao.portal.document.service.intervene.DomainBlackService;
import com.iflytek.lynxiao.portal.document.service.site.SiteVersionService;
import com.iflytek.lynxiao.portal.eval.component.checker.ExistChecker;
import com.iflytek.lynxiao.portal.eval.component.checker.impl.ExistChecker4Dataset;
import com.iflytek.lynxiao.portal.eval.component.checker.impl.ExistChecker4Idx;
import com.iflytek.lynxiao.portal.eval.component.checker.impl.ExistChecker4Site;
import com.iflytek.lynxiao.portal.eval.component.domain.ExistCheckResult;
import com.iflytek.lynxiao.portal.eval.component.domain.IdxWithSiteDTO;
import com.iflytek.lynxiao.portal.eval.component.domain.PreEvalDoc;
import com.iflytek.lynxiao.resource.generated.domain.GeneratedIdxDbInst;
import com.iflytek.lynxiao.resource.repository.DatasetVersionSiteVersionRepository;
import com.iflytek.lynxiao.resource.repository.IdxDbInstRepository;
import com.iflytek.turing.astrolink.service.dto.WorkflowComponentParameter;
import com.iflytek.turing.astrolink.service.dto.WorkflowProcess;
import com.iflytek.turing.astrolink.service.dto.WorkflowProcessNode;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import skynet.boot.pandora.exception.PandoraException;

import java.util.*;
import java.util.stream.Collectors;

import static com.iflytek.lynxiao.portal.eval.utils.WorkflowUtil.RECALL_SEMTRC;
import static com.iflytek.lynxiao.portal.eval.utils.WorkflowUtil.RECALL_TEXTRC;

/**
 * 竞品分析Url是否存在特征库中
 *
 * <AUTHOR>  2025/3/6 09:12
 */

@Slf4j
@Service
public class ExistService {

    private final DatasetVersionSiteVersionRepository datasetVersionSiteVersionRepository;
    private final SiteVersionService siteVersionService;
    private final IdxDbInstRepository idxDbInstRepository;
    private final UrlDocConverter urlDocConverter;
    private final List<ExistChecker> checkers = new ArrayList<>();

    public ExistService(DatasetVersionSiteVersionRepository datasetVersionSiteVersionRepository,
                        SiteVersionService siteVersionService, DocumentFetchService documentFetchService,
                        DomainBlackService domainBlackService, IdxDbInstRepository idxDbInstRepository,
                        UrlDocConverter urlDocConverter, DatashardDecoder datashardDecoder) {
        this.datasetVersionSiteVersionRepository = datasetVersionSiteVersionRepository;
        this.siteVersionService = siteVersionService;
        this.idxDbInstRepository = idxDbInstRepository;
        this.urlDocConverter = urlDocConverter;
        this.checkers.add(new ExistChecker4Idx(domainBlackService, documentFetchService));
        this.checkers.add(new ExistChecker4Dataset(documentFetchService, datashardDecoder));
        this.checkers.add(new ExistChecker4Site(documentFetchService, datashardDecoder));
    }

    public ExistCheckResult process(String id, String url, WorkflowProcess workflowProcess, GoodCaseContext goodCaseContext) {
        ExistCheckResult respPayload = new ExistCheckResult();

        // 获取索引库
        Set<String> indexCodes = findIndexCodes(workflowProcess);
        log.debug("indexCodes:{}", indexCodes);

        // 获取关联的数据集、站点信息
        List<IdxWithSiteDTO> idxWithSiteDTOList = parseSiteList(indexCodes);
        if (CollectionUtils.isEmpty(idxWithSiteDTOList)) {
            return respPayload;
        }
        respPayload.setIdxWithSiteDTOList(idxWithSiteDTOList);

        for (ExistChecker checker : checkers) {
            long startCost = System.currentTimeMillis();
            boolean isStop = checker.check(respPayload, id, url, idxWithSiteDTOList, goodCaseContext);
            log.info("checker:{} check id:{} or url:{} cost:{}ms isStop:{}", checker.getClass().getSimpleName(), id, url, System.currentTimeMillis() - startCost, isStop);
            if (isStop) {
                break;
            }
        }

        if (goodCaseContext.getDoc() == null && StringUtils.isNotBlank(url)) {
            Optional<PreEvalDoc> crawledDoc = urlDocConverter.fetchDoc(url, false, null);
            goodCaseContext.setDoc(JSONObject.from(crawledDoc.orElse(null)));
        }

        log.debug("id:{}, url:{} url exist check result:{}", id, url, respPayload);
        return respPayload;
    }

    /**
     * 根据索引库名 获取关联的数据集、站点信息
     */
    public List<IdxWithSiteDTO> parseSiteList(Set<String> indexCodes) {

        List<IdxWithSiteDTO> idxWithSiteDTOList = new ArrayList<>();
        for (String idxCode : indexCodes) {
            Optional<GeneratedIdxDbInst> idxDbInst = this.idxDbInstRepository.findByCode(idxCode);
            if (idxDbInst.isEmpty()) {
                log.debug("can not found idxCode:{} in idxDbInstRepository", idxCode);
                continue;
            }
            GeneratedIdxDbInst idxDbInstDTO = idxDbInst.get();
            IdxWithSiteDTO idxDbContext = new IdxWithSiteDTO().setId(String.valueOf(idxDbInstDTO.getId())).setCode(idxCode)
                    .setType(idxDbInstDTO.getType())
                    .setIdRegion(idxDbInstDTO.getIdInRegion())
                    .setDatasetId(String.valueOf(idxDbInstDTO.getDatasetId()))
                    .setRegion(idxDbInstDTO.getRegion())
                    .setDatasetVersionId(String.valueOf(idxDbInstDTO.getDatasetVersionId()));
            idxWithSiteDTOList.add(idxDbContext);
        }
        log.debug("idxDbContextList:{}", idxWithSiteDTOList);

        //完善站点信息
        for (IdxWithSiteDTO checkContext : idxWithSiteDTOList) {
            List<SiteVersionGroupDTO> siteList = siteVersionService.findByDatasetVersionId(checkContext.getDatasetVersionId());
            checkContext.setSiteIds(siteList.stream().map(SiteVersionGroupDTO::getSiteDTO).collect(Collectors.toList()));

            List<Long> siteVersionIds = datasetVersionSiteVersionRepository.findSiteVersionIds(Long.parseLong(checkContext.getDatasetVersionId()));
            checkContext.setSiteVersionIds(siteVersionIds);
        }

        return idxWithSiteDTOList;
    }

    public Set<String> findIndexCodes(WorkflowProcess workflowProcess) {
        Set<String> idxCodes = new HashSet<>();
        //获取流程拓扑
        List<WorkflowProcessNode> nodeList = workflowProcess.getNodeList();
        List<String> recallNodeCodes = Arrays.asList(RECALL_TEXTRC, RECALL_SEMTRC);

        for (String recallNodeCode : recallNodeCodes) {
            WorkflowProcessNode targetNode = null;
            for (WorkflowProcessNode processNode : nodeList) {
                if (processNode.getComponent() != null && recallNodeCode.equals(processNode.getComponent().getCode())) {
                    targetNode = processNode;
                }
            }

            if (targetNode == null) {
                continue;
            }

            try {
                List<WorkflowComponentParameter> params = targetNode.getComponent().getInputArgs().stream().filter(item -> "params".equals(item.getKey())).toList();
                WorkflowComponentParameter componentParameter = params.getFirst();
                JSONArray value = JSONArray.from(componentParameter.getValue());
                JSONArray indexCodes = value.getJSONObject(0).getJSONArray("indexCodes");
                idxCodes.addAll(indexCodes.toJavaList(String.class));
            } catch (Exception e) {
                log.error("parse indexCodes error.", e);
                throw new PandoraException("parse indexCodes error.");
            }
        }
        if (CollectionUtils.isEmpty(idxCodes)) {
            throw new PandoraException(String.format("no idxCodes found in the process:%s", workflowProcess.getCode()));
        }
        return idxCodes;
    }
}
