package com.iflytek.lynxiao.portal.eval;

import com.iflytek.lynxiao.portal.PortalTestBoot;
import com.iflytek.lynxiao.portal.eval.component.core.ExistService;
import com.iflytek.turing.astrolink.service.WorkflowProcessService;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit.jupiter.SpringExtension;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.Set;

@Slf4j
@ExtendWith(SpringExtension.class)
@RunWith(SpringRunner.class)
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT, classes = PortalTestBoot.class)
@ActiveProfiles("dev")
public class ExistServiceTest {

    @Autowired
    private WorkflowProcessService workflowProcessService;

    @Autowired
    private ExistService existService;

    @Test
    public void testFindIdxCodeFromWorkflowProcess(){

        String processId = "68c0d5a5e84e1fe0bc0c1fe8";
        Set<String> indexCodes = existService.findIndexCodes(workflowProcessService.getWorkflowProcess(processId, false));
        System.out.println(indexCodes);

    }
}
